#!/usr/bin/env Rscript
# 检查数据文件结构

library(readxl)
library(dplyr)

# 检查基线蛋白质数据
cat("基线蛋白质数据结构:\n")
protein_baseline <- read_excel("基线蛋白组学(1-stage2_vs_1-stage1_annotation).xlsx")
cat("列名:", paste(colnames(protein_baseline), collapse = ", "), "\n")
cat("维度:", dim(protein_baseline), "\n")
cat("前几行:\n")
print(head(protein_baseline, 3))

cat("\n===================\n")

# 检查基线代谢物数据
cat("基线代谢物数据结构:\n")
metabolite_baseline <- read_excel("基线代谢组学（1-stage2_vs_1-stage1_info）.xlsx")
cat("列名:", paste(colnames(metabolite_baseline), collapse = ", "), "\n")
cat("维度:", dim(metabolite_baseline), "\n")
cat("前几行:\n")
print(head(metabolite_baseline, 3))

cat("\n===================\n")

# 检查纵向蛋白质数据
cat("纵向蛋白质数据结构:\n")
protein_treatment <- read_excel("纵向蛋白组学（2_vs_1_annotation）.xlsx")
cat("列名:", paste(colnames(protein_treatment), collapse = ", "), "\n")
cat("维度:", dim(protein_treatment), "\n")
cat("前几行:\n")
print(head(protein_treatment, 3))

cat("\n===================\n")

# 检查纵向代谢物数据
cat("纵向代谢物数据结构:\n")
metabolite_treatment <- read_excel("纵向代谢组学（2_vs_1_info）.xlsx")
cat("列名:", paste(colnames(metabolite_treatment), collapse = ", "), "\n")
cat("维度:", dim(metabolite_treatment), "\n")
cat("前几行:\n")
print(head(metabolite_treatment, 3))
