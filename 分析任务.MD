# **研究总纲与实施方案：基于多组学技术的PDR相关牵拉性视网膜脱离病理机制研究**

## **1.0 研究背景 (Introduction)**

增殖性糖尿病视网膜病变（Proliferative Diabetic Retinopathy, PDR）是糖尿病最严重的微血管并发症之一，其终末期特征为牵拉性视网膜脱离（Tractional Retinal Detachment, TRD），是导致患者不可逆性视力损伤的主要原因。玻璃体切除术（Pars Plana Vitrectomy, PPV）是治疗TRD的唯一有效手段。为降低PPV术中由新生血管（Neovascularization, NV）破裂引起的出血风险、提高手术成功率，术前玻璃体腔注射抗血管内皮生长因子（Anti-VEGF）药物已成为临床常规。

然而，临床观察揭示了一个重要的悖论：抗VEGF药物在有效抑制血管活性的同时，可能诱导或加剧玻璃体内纤维组织的增生与收缩，导致TRD病情恶化，此现象被称为“紧缩综合征”（Crunch Syndrome）。这一矛盾现象提示，在VEGF信号通路被抑制的背景下，可能存在其他未被充分认识的、主导纤维化进程的分子通路被代偿性激活。

因此，本研究的核心科学假说为：在PDR-TRD的复杂分子网络中，VEGF信号通路不仅驱动血管生成，还与其他关键信号通路（尤其是炎症与纤维化通路）存在着动态的、相互拮抗或制衡的关系。因此，急性、强力的抗VEGF干预，在有效抑制血管的同时，会打破这种脆弱的病理平衡，导致原本被相对抑制的促纤维化通路（pro-fibrotic pathways）被代偿性地、过度地激活，从而在分子层面驱动了“紧缩综合征”的发生。

本研究旨在通过高通量蛋白质组学和代谢组学技术，系统性地解析TRD患者在基线状态及抗VEGF干预后的眼内分子环境重编程，以期：
1.  识别并验证定义TRD易感性的关键生物分子网络。
2.  阐明抗VEGF药物诱导纤维化加剧的分子机制。
3.  发掘可用于预测TRD进展和治疗反应的生物标志物及潜在的抗纤维化治疗靶点。

## **2.0 研究设计与分析策略 (Methods)**

### **2.1 研究队列定义**
本研究采用前瞻性病例对照设计，纳入PDR患者并根据其基线临床表现分为两组：
*   **对照组 (Stage 1): 单纯性玻璃体出血组 (Simple Vitreous Hemorrhage, SVH)**。定义为PDR伴玻璃体出血，但无TRD的患者。此组代表了PDR的增殖期，但未进入终末纤维化阶段。
*   **病例组 (Stage 2): 牵拉性视网膜脱离组 (Tractional Retinal Detachment, TRD)**。定义为PDR伴玻璃体出血和TRD的患者。此组代表了PDR的终末纤维化阶段。

### **2.2 样本采集与多组学分析**
于PPV术中采集所有入组患者的未稀释玻璃体液样本。样本被立即处理并用于非标记定量蛋白质组学（Label-Free Quantification Proteomics）和广泛靶向代谢组学（Widely-Targeted Metabolomics）分析。

### **2.3 数据分析逻辑**
分析流程遵循两步走策略，以解构疾病的静态特征与动态变化：
1.  **基线差异分析 (Baseline Comparison):** 比较Stage 2 (TRD) 与 Stage 1 (SVH) 组在治疗前的蛋白质组和代谢组谱，旨在识别与TRD固有病理状态相关的分子特征。**(此为Figure 1的分析基础)**
2.  **干预效应分析 (Interventional Effect Analysis):** 对比Stage 2 (TRD) 组内患者在接受抗VEGF注射前与注射后的多组学数据，旨在揭示抗VEGF药物对TRD眼内微环境的精确分子调控作用，特别是纤维化相关通路的动态变化。**(此为Figure 2及后续分析的基础)**

## **3.0 图表呈现策略 (Figure Strategy)**

*   **Figure 1: Characterization of the Baseline Molecular Landscape in TRD.**
    *   **Objective:** To establish the foundational hypothesis that TRD is characterized by a distinct, pre-existing molecular microenvironment. This figure will systematically present the differential expression of proteins and metabolites between the TRD and SVH cohorts at baseline.
*   **Figure 2: The Paradoxical Molecular Response to Anti-VEGF Intervention in TRD.**
    *   **Objective:** To provide direct evidence for the "crunch syndrome" at the molecular level. This figure will quantitatively demonstrate the simultaneous suppression of angiogenic factors and the up-regulation of pro-fibrotic mediators post-injection.
*   **Figure 3 & 4: Mechanistic Elucidation and Biomarker Discovery.**
    *   **Objective:** To synthesize the findings into a coherent mechanistic model and translate them into clinical applications. Figure 3 will delineate the correlation networks among key differential molecules. Figure 4 will evaluate the diagnostic/prognostic potential of candidate biomarkers using ROC curve analysis and present a conclusive summary model.

---

## **4.0 AI任务技术指令：Figure 1 生成规程**

好的，收到。您的指示非常明确：**明确区分蛋白质组和代谢组学的差异筛选标准，并仅为Figure 1生成具备可执行性的技术指令。**

我将完全遵照您提供的最新、最精确的筛选标准来重构任务指令。这份指令将直接反映您的分析策略，确保AI的输出与您的研究设计无缝对接。

---

# **AI任务技术指令：Figure 1 生成规程 (V3.1 - 更新筛选标准)**

**核心科学目标:** 全面、定量地展示TRD（Stage 2）相较于SVH（Stage 1）在治疗基线状态下，其固有的、高度失衡的病理分子微环境，从而为后续的机制探索提供背景。

**输入数据文件:**
*   蛋白质组学: `基线蛋白组学(1-stage2_vs_1-stage1_annotation).xlsx` (需包含 `FC`, `P-value` 列)
*   代谢组学: `基线代谢组学（1-stage2_vs_1-stage1_info）.xlsx` (需包含 `VIP`, `P-value`, `FC` 列)
*   样本分组与丰度矩阵: (隐含于上述文件或需额外提供)

**执行规程:**

### **Step 1: Panel A - 差异表达火山图 (Volcano Plots)**
**任务:** 可视化蛋白质组与代谢组在TRD vs. SVH组间的整体差异表达分布。

1.1.  **布局:** 初始化一个1行2列的matplotlib子图对象 (`fig, (ax1, ax2)`)。坐标轴统一设置为 X轴: "Log₂ (Fold Change)"，Y轴: "-Log₁₀ (P-value)"。

1.2.  **绘制ax1 (蛋白质组火山图):**
    *   **数据处理:** 为蛋白质组数据计算 `Log2FC` 列 (`np.log2(P_data['FC'])`)。
    *   **阈值定义 (严格遵循):**
        *   上调 (Up-regulated): `FC ≥ 1.5` AND `P-value ≤ 0.05`。
        *   下调 (Down-regulated): `FC ≤ 0.6667` AND `P-value ≤ 0.05`。
        *   无显著差异 (Non-significant): 其他所有数据点。
    *   **渲染:** 上调点渲染为**红色** (#D62728)，下调点渲染为**蓝色** (#1F77B4)，其余为**灰色** (#7F7F7F)。
    *   **标注:** 在图上清晰标注代表性蛋白质的基因名（例如 `CAST`, `GALNS`, `ALB`），确保标签不重叠。
    *   **标题:** "Proteomic Profile at Baseline (TRD vs. SVH)"。

1.3.  **绘制ax2 (代谢组火山图):**
    *   **数据处理:** 为代谢组数据计算 `Log2FC` 列 (`np.log2(M_data['FC'])`)。
    *   **阈值定义 (严格遵循):**
        *   显著差异 (Significant): `VIP > 1` AND `P-value < 0.05`。
    *   **渲染:**
        *   同时满足 `VIP > 1` 和 `P-value < 0.05` 的数据点根据其FC值进行着色：若`FC ≥ 1`，渲染为**红色**；若`FC < 1`，渲染为**蓝色**。
        *   不满足上述条件的其他所有数据点，渲染为**灰色**。
    *   **标注:** 清晰标注代表性代谢物的名称（例如 `3-hydroxypropanoic acid`, `L-Glutamic acid`）。
    *   **标题:** "Metabolomic Profile at Baseline (TRD vs. SVH)"。

### **Step 2: Panel B - 层次聚类热图 (Hierarchical Clustering Heatmap)**
**任务:** 展示关键差异分子的表达模式，以突显其作为“TRD分子指纹”的潜力。

2.1.  **特征筛选:**
    *   **差异蛋白质筛选:** 筛选所有同时满足 `(FC ≥ 1.5 or FC ≤ 0.6667)` AND `P-value ≤ 0.05` 的蛋白质。
    *   **差异代谢物筛选:** 筛选所有同时满足 `VIP > 1` AND `P-value < 0.05` 的代谢物。
    *   **顶层分子选择:** 从上述筛选出的差异蛋白质和差异代谢物列表中，**分别按P-value升序排序，各取前15个特征分子**，共计30个分子用于绘图。

2.2.  **矩阵构建与标准化:**
    *   构建一个包含30个特征分子（行）和所有研究样本（列）的丰度矩阵。
    *   对该矩阵进行**行向Z-score标准化**，以确保不同分子的表达量在同一可比范围内。

2.3.  **热图绘制 (`seaborn.clustermap`):**
    *   对矩阵的行（分子）和列（样本）进行层次聚类。
    *   在热图的**列顶端**添加颜色注释条，用以明确区分"SVH" (Stage 1) 和 "TRD" (Stage 2) 组。
    *   采用蓝-白-红发散色谱（`cmap='RdBu_r'`）。
    *   **标题:** "Molecular Signature of the TRD Baseline State"。

### **Step 3: Panel C - 联合通路富集分析气泡图 (Joint Pathway Analysis)**
**任务:** 揭示TRD基线状态下，哪些生物学功能和通路已发生系统性紊乱。

3.1.  **输入集构建:**
    *   创建一个**整合的差异分子列表**，该列表包含**所有**满足以下条件的分子：
        *   所有符合 `(FC ≥ 1.5 or FC ≤ 0.6667)` AND `P-value ≤ 0.05` 标准的蛋白质（使用其对应的基因名）。
        *   所有符合 `VIP > 1` AND `P-value < 0.05` 标准的代谢物（使用其标准化合物名）。

3.2.  **富集分析:**
    *   使用该整合列表作为输入，针对KEGG（或GO）数据库进行通路富集分析（Over-Representation Analysis）。

3.3.  **气泡图绘制:**
    *   Y轴: 富集通路的名称（建议展示FDR最低的Top 20条通路）。
    *   X轴: 富集比率 (Rich Factor)。
    *   气泡大小: 与命中该通路的差异分子数量（Hit Count）成正比。
    *   气泡颜色: 与富集显著性（FDR或P-value）关联，FDR越低，颜色越显著（例如，从红色到黄色的渐变）。
    *   **标题:** "Perturbed Biological Pathways in the TRD Baseline State"。

### **Step 4: 最终整合与输出**
4.1.  将生成的Panel A, B, C标准地组合成单张Figure 1。
4.2.  在各面板左上角添加大写字母标识 "A", "B", "C"。
4.3.  检查并统一所有图表的字体大小、线条粗细和分辨率，确保其达到学术出版要求。